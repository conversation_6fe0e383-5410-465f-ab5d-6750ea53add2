import json
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone

from customer._services.workflow_registry import get_workflow_registry
from customer._services.workflow_config_manager import WorkflowConfigManager
from customer._services.workflow_config_validator import WorkflowConfigValidator


class Command(BaseCommand):
    help = 'Manage workflow configurations'

    def add_arguments(self, parser):
        subparsers = parser.add_subparsers(dest='action', help='Available actions')
        
        # List workflows
        list_parser = subparsers.add_parser('list', help='List all workflows')
        list_parser.add_argument('--category', help='Filter by category')
        list_parser.add_argument('--include-invalid', action='store_true', help='Include invalid workflows')
        
        # Validate workflows
        validate_parser = subparsers.add_parser('validate', help='Validate workflow configurations')
        validate_parser.add_argument('--workflow-id', help='Validate specific workflow')
        
        # Show workflow details
        show_parser = subparsers.add_parser('show', help='Show workflow details')
        show_parser.add_argument('workflow_id', help='Workflow ID to show')
        
        # Create workflow
        create_parser = subparsers.add_parser('create', help='Create new workflow from template')
        create_parser.add_argument('workflow_id', help='New workflow ID')
        create_parser.add_argument('--template', default='basic', help='Template to use')
        create_parser.add_argument('--name', help='Workflow display name')
        
        # Statistics
        stats_parser = subparsers.add_parser('stats', help='Show workflow statistics')
        
        # Reload workflows
        reload_parser = subparsers.add_parser('reload', help='Reload workflow configurations')
        reload_parser.add_argument('--workflow-id', help='Reload specific workflow')

    def handle(self, *args, **options):
        action = options['action']
        
        if not action:
            self.print_help('manage_workflows', '')
            return
        
        try:
            if action == 'list':
                self.handle_list(options)
            elif action == 'validate':
                self.handle_validate(options)
            elif action == 'show':
                self.handle_show(options)
            elif action == 'create':
                self.handle_create(options)
            elif action == 'stats':
                self.handle_stats(options)
            elif action == 'reload':
                self.handle_reload(options)
            else:
                raise CommandError(f"Unknown action: {action}")
                
        except Exception as e:
            raise CommandError(f"Command failed: {str(e)}")

    def handle_list(self, options):
        """Handle list command"""
        registry = get_workflow_registry()
        workflows = registry.list_workflows(
            category=options.get('category'),
            include_invalid=options.get('include_invalid', False)
        )
        
        if not workflows:
            self.stdout.write(self.style.WARNING('No workflows found'))
            return
        
        self.stdout.write(self.style.SUCCESS(f'Found {len(workflows)} workflows:'))
        self.stdout.write('')
        
        # Table header
        self.stdout.write(f"{'ID':<25} {'Name':<30} {'Version':<10} {'Category':<15} {'Schema':<8} {'Status':<8}")
        self.stdout.write('-' * 100)
        
        for workflow in workflows:
            status = 'Valid' if workflow.is_valid else 'Invalid'
            status_style = self.style.SUCCESS if workflow.is_valid else self.style.ERROR
            
            self.stdout.write(
                f"{workflow.id:<25} "
                f"{workflow.name[:29]:<30} "
                f"{workflow.version:<10} "
                f"{workflow.category:<15} "
                f"{workflow.schema_version:<8} "
                f"{status_style(status):<8}"
            )

    def handle_validate(self, options):
        """Handle validate command"""
        workflow_id = options.get('workflow_id')
        
        if workflow_id:
            # Validate specific workflow
            registry = get_workflow_registry()
            result = registry.validate_workflow(workflow_id)
            
            self.stdout.write(f"Validation result for workflow '{workflow_id}':")
            
            if result.is_valid:
                self.stdout.write(self.style.SUCCESS('✓ Valid'))
            else:
                self.stdout.write(self.style.ERROR('✗ Invalid'))
                self.stdout.write('')
                self.stdout.write('Errors:')
                for error in result.errors:
                    self.stdout.write(f"  - {error}")
            
            if result.warnings:
                self.stdout.write('')
                self.stdout.write('Warnings:')
                for warning in result.warnings:
                    self.stdout.write(self.style.WARNING(f"  - {warning}"))
            
            if result.suggestions:
                self.stdout.write('')
                self.stdout.write('Suggestions:')
                for suggestion in result.suggestions:
                    self.stdout.write(f"  - {suggestion}")
        else:
            # Validate all workflows
            manager = WorkflowConfigManager()
            results = manager.validate_all_workflows()
            
            valid_count = sum(1 for result in results.values() if result.is_valid)
            total_count = len(results)
            
            self.stdout.write(f"Validation results: {valid_count}/{total_count} workflows are valid")
            self.stdout.write('')
            
            for workflow_id, result in results.items():
                if result.is_valid:
                    self.stdout.write(self.style.SUCCESS(f"✓ {workflow_id}"))
                else:
                    self.stdout.write(self.style.ERROR(f"✗ {workflow_id}"))
                    for error in result.errors[:3]:  # Show first 3 errors
                        self.stdout.write(f"    {error}")
                    if len(result.errors) > 3:
                        self.stdout.write(f"    ... and {len(result.errors) - 3} more errors")

    def handle_show(self, options):
        """Handle show command"""
        workflow_id = options['workflow_id']
        registry = get_workflow_registry()
        
        workflow_info = registry.get_workflow_info(workflow_id)
        if not workflow_info:
            raise CommandError(f"Workflow not found: {workflow_id}")
        
        config = registry.get_workflow(workflow_id)
        
        self.stdout.write(self.style.SUCCESS(f"Workflow: {workflow_id}"))
        self.stdout.write('')
        
        # Basic info
        self.stdout.write(f"Name: {workflow_info.name}")
        self.stdout.write(f"Version: {workflow_info.version}")
        self.stdout.write(f"Description: {workflow_info.description}")
        self.stdout.write(f"Category: {workflow_info.category}")
        self.stdout.write(f"Schema Version: {workflow_info.schema_version}")
        self.stdout.write(f"File Path: {workflow_info.file_path}")
        self.stdout.write(f"Loaded At: {workflow_info.loaded_at}")
        self.stdout.write(f"Status: {'Valid' if workflow_info.is_valid else 'Invalid'}")
        
        if workflow_info.tags:
            self.stdout.write(f"Tags: {', '.join(workflow_info.tags)}")
        
        if not workflow_info.is_valid:
            self.stdout.write('')
            self.stdout.write(self.style.ERROR('Validation Errors:'))
            for error in workflow_info.validation_errors:
                self.stdout.write(f"  - {error}")
        
        # Show configuration summary
        if config:
            self.stdout.write('')
            self.stdout.write('Configuration Summary:')
            
            if workflow_info.schema_version == "2.0":
                steps = config.get('steps', [])
                self.stdout.write(f"  Steps: {len(steps)}")
                for step in steps:
                    self.stdout.write(f"    - {step.get('id', 'unknown')}: {step.get('name', 'unknown')} ({step.get('type', 'unknown')})")
            else:
                steps = config.get('steps', [])
                self.stdout.write(f"  Steps: {len(steps)}")
                for step in steps:
                    self.stdout.write(f"    - {step.get('id', 'unknown')}: {step.get('name', 'unknown')}")

    def handle_create(self, options):
        """Handle create command"""
        workflow_id = options['workflow_id']
        template = options.get('template', 'basic')
        name = options.get('name', workflow_id.replace('_', ' ').title())

        manager = WorkflowConfigManager()

        self.stdout.write(f"Creating workflow '{workflow_id}' from template '{template}'...")

        success = manager.create_workflow_from_template(template, workflow_id, name)

        if success:
            self.stdout.write(self.style.SUCCESS(f"✓ Successfully created workflow: {workflow_id}"))
        else:
            self.stdout.write(self.style.ERROR(f"✗ Failed to create workflow: {workflow_id}"))

    def handle_stats(self, options):
        """Handle stats command"""
        manager = WorkflowConfigManager()
        stats = manager.get_workflow_statistics()
        
        self.stdout.write(self.style.SUCCESS('Workflow Statistics:'))
        self.stdout.write('')
        
        self.stdout.write(f"Total Workflows: {stats['total_workflows']}")
        self.stdout.write(f"Valid Workflows: {stats['valid_workflows']}")
        self.stdout.write(f"Invalid Workflows: {stats['invalid_workflows']}")
        self.stdout.write('')
        
        self.stdout.write('By Schema Version:')
        for version, count in stats['by_schema_version'].items():
            self.stdout.write(f"  {version}: {count}")
        
        self.stdout.write('')
        self.stdout.write('By Category:')
        for category, count in stats['by_category'].items():
            self.stdout.write(f"  {category}: {count}")

    def handle_reload(self, options):
        """Handle reload command"""
        workflow_id = options.get('workflow_id')
        registry = get_workflow_registry()
        
        if workflow_id:
            self.stdout.write(f"Reloading workflow: {workflow_id}")
            success = registry.reload_workflow(workflow_id)
            
            if success:
                self.stdout.write(self.style.SUCCESS(f"✓ Successfully reloaded workflow: {workflow_id}"))
            else:
                self.stdout.write(self.style.ERROR(f"✗ Failed to reload workflow: {workflow_id}"))
        else:
            self.stdout.write("Reloading all workflows...")
            registry.reload_workflows()
            self.stdout.write(self.style.SUCCESS("✓ Successfully reloaded all workflows"))
