{"schema_version": "2.0", "workflow": {"id": "bvtpa_policy_details", "name": "BVTPA Policy Details Workflow", "version": "2.0.0", "description": "Retrieves detailed policy information and claims data from BVTPA API", "category": "policy_management", "tags": ["bvtpa", "policy", "details", "claims", "insurance"]}, "configuration": {"data_source": {"mode": "database", "fallback_mode": "fixed", "database_queries": [{"id": "platform_identity", "table": "customer_customerplatformidentity", "fields": {"social_id": "platform_user_id", "channel_id": "channel_id", "channel": "platform"}, "where": "id = :platform_id AND is_active = true", "required": true}, {"id": "customer_data", "table": "customer_customer", "fields": {"citizen_id": "national_id"}, "where": "customer_id = :customer_id", "required": true}], "fixed_values": {"social_id": "U3ef2199803607a9ec643f2461fd2f039", "channel_id": "2006769099", "citizen_id": "2019086318637", "channel": "LINE"}}, "execution": {"timeout_minutes": 5, "retry_policy": {"default_max_retries": 3, "default_delay_seconds": 3, "backoff_strategy": "exponential"}, "cache": {"enabled": true, "duration_minutes": 60, "key_template": "policy_details_{customer_id}_{platform_id}_{member_code}"}}}, "steps": [{"id": "authenticate", "name": "get_bearer_token", "type": "http_request", "description": "Authenticate with BVTPA API to obtain bearer token", "config": {"endpoint": "/api/GetToken", "method": "POST", "headers": {"Content-Type": "application/x-www-form-urlencoded"}, "request_body": {"USERNAME": "BVTPA", "PASSWORD": "*d!n^+Cb@1", "SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}"}, "response_extraction": {"bearer_token": {"path": "$", "type": "string", "required": true, "description": "Bearer token for API authentication"}}, "retry": {"max_attempts": 3, "delay_seconds": 2}}}, {"id": "fetch_policy_details", "name": "fetch_policy_details", "type": "http_request", "description": "Retrieve detailed policy information and claims data", "depends_on": ["authenticate"], "config": {"endpoint": "/api/PolicyDetailSocial", "method": "POST", "headers": {"Authorization": "Bearer {{bearer_token}}", "Content-Type": "application/json"}, "request_body": {"SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}", "MEMBER_CODE": "{{member_code}}"}, "response_extraction": {"policy_details": {"path": "$.ListOfPolDet", "type": "array", "required": true, "description": "Detailed policy information"}, "claims_data": {"path": "$.ListOfPolClaim", "type": "array", "required": false, "description": "Policy claims information"}}, "validation": {"rules": [{"id": "policy_details_exist", "type": "json_path", "path": "$.ListOfPolDet", "operator": "not_empty", "error_message": "No policy details found for member code", "warning_only": false}, {"id": "valid_member_code", "type": "custom_function", "function": "validate_member_code", "parameters": {"member_code": "{{member_code}}"}, "error_message": "Invalid member code format"}]}, "retry": {"max_attempts": 2, "delay_seconds": 3}}}], "output": {"storage": {"enabled": true, "table": "CustomerPolicyDetails", "key_fields": ["customer_id", "member_code"], "data_mapping": {"customer_id": "{{input.customer_id}}", "member_code": "{{input.member_code}}", "citizen_id": "{{citizen_id}}", "policy_details": "{{policy_details}}", "claims_data": "{{claims_data}}", "last_updated": "{{timestamp}}"}}, "response_format": {"type": "processed", "processor": "policy_details_processor", "include_metadata": true}}, "validation": {"input_schema": {"customer_id": {"type": "integer", "required": true, "min": 1, "description": "Customer ID from database"}, "platform_id": {"type": "integer", "required": true, "min": 1, "description": "Platform identity ID"}, "member_code": {"type": "string", "required": true, "min_length": 1, "max_length": 100, "pattern": "^[A-Z0-9]+$", "description": "Policy member code"}}, "business_rules": [{"id": "customer_exists", "type": "database_check", "query": "SELECT 1 FROM customer_customer WHERE customer_id = :customer_id", "error_message": "Customer not found in database"}, {"id": "platform_identity_exists", "type": "database_check", "query": "SELECT 1 FROM customer_customerplatformidentity WHERE id = :platform_id AND is_active = true", "error_message": "Platform identity not found or inactive"}, {"id": "member_code_belongs_to_customer", "type": "database_check", "query": "SELECT 1 FROM CustomerPolicyList WHERE customer_id = :customer_id AND JSON_CONTAINS(member_codes, JSON_QUOTE(:member_code))", "error_message": "Member code does not belong to this customer", "warning_only": true}]}, "metadata": {"created_by": "system", "created_at": "2024-01-01T00:00:00Z", "last_modified": "2024-01-01T00:00:00Z", "documentation_url": "https://docs.example.com/workflows/bvtpa-policy-details", "support_contact": "<EMAIL>"}}