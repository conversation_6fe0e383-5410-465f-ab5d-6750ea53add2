{"schema_version": "2.0", "workflow": {"id": "bvtpa_policy_list", "name": "BVTPA Policy List Workflow", "version": "2.0.0", "description": "Retrieves policy list from BVTPA API with enhanced configuration support", "category": "policy_management", "tags": ["bvtpa", "policy", "list", "insurance"]}, "configuration": {"data_source": {"mode": "database", "fallback_mode": "fixed", "database_queries": [{"id": "platform_identity", "table": "customer_customerplatformidentity", "fields": {"social_id": "platform_user_id", "channel_id": "channel_id", "channel": "platform"}, "where": "id = :platform_id AND is_active = true", "required": true}, {"id": "customer_data", "table": "customer_customer", "fields": {"citizen_id": "national_id"}, "where": "customer_id = :customer_id", "required": true}], "fixed_values": {"social_id": "U3ef2199803607a9ec643f2461fd2f039", "channel_id": "2006769099", "citizen_id": "2019086318637", "channel": "LINE"}}, "execution": {"timeout_minutes": 5, "retry_policy": {"default_max_retries": 3, "default_delay_seconds": 3, "backoff_strategy": "exponential"}, "cache": {"enabled": true, "duration_minutes": 60, "key_template": "policy_list_{customer_id}_{platform_id}"}}}, "steps": [{"id": "authenticate", "name": "get_bearer_token", "type": "http_request", "description": "Authenticate with BVTPA API to obtain bearer token", "config": {"endpoint": "/api/GetToken", "method": "POST", "headers": {"Content-Type": "application/x-www-form-urlencoded"}, "request_body": {"USERNAME": "BVTPA", "PASSWORD": "*d!n^+Cb@1", "SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}"}, "response_extraction": {"bearer_token": {"path": "$", "type": "string", "required": true, "description": "Bearer token for API authentication"}}, "retry": {"max_attempts": 3, "delay_seconds": 2}}}, {"id": "verify_citizen", "name": "verify_citizen_id", "type": "http_request", "description": "Verify citizen ID with BVTPA system", "depends_on": ["authenticate"], "config": {"endpoint": "/api/SearchCitizenID", "method": "POST", "headers": {"Authorization": "Bearer {{bearer_token}}", "Content-Type": "application/json"}, "request_body": {"SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}"}, "validation": {"rules": [{"id": "citizen_id_status_check", "type": "json_path", "path": "$.ListOfSearchCitizenID[0].Status", "operator": "equals", "value": "1", "error_message": "Citizen ID verification failed - invalid status"}, {"id": "citizen_id_results_exist", "type": "json_path", "path": "$.ListOfSearchCitizenID", "operator": "not_empty", "error_message": "No citizen ID search results found"}]}, "retry": {"max_attempts": 2, "delay_seconds": 3}}}, {"id": "verify_registration", "name": "verify_registration", "type": "http_request", "description": "Verify customer registration status", "depends_on": ["authenticate"], "config": {"endpoint": "/api/CheckRegister", "method": "POST", "headers": {"Authorization": "Bearer {{bearer_token}}", "Content-Type": "application/json"}, "request_body": {"CITIZEN_ID": "{{citizen_id}}", "SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}", "CONTACT": ""}, "validation": {"rules": [{"id": "registration_status_check", "type": "json_path", "path": "$.ListOfCheckRegister[0].Status", "operator": "equals", "value": "YES", "error_message": "Registration verification failed - customer not registered"}]}, "retry": {"max_attempts": 2, "delay_seconds": 3}}}, {"id": "fetch_policies", "name": "fetch_policy_list", "type": "http_request", "description": "Retrieve policy list from BVTPA API", "depends_on": ["authenticate", "verify_citizen", "verify_registration"], "config": {"endpoint": "/api/PolicyListSocial", "method": "POST", "headers": {"Authorization": "Bearer {{bearer_token}}", "Content-Type": "application/json"}, "request_body": {"CITIZEN_ID": "{{citizen_id}}", "SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}"}, "response_extraction": {"policies": {"path": "$.ListOfPolicyListSocial", "type": "array", "required": true, "description": "List of customer policies"}, "member_codes": {"path": "$.ListOfPolicyListSocial[*].MemberCode", "type": "array", "required": false, "description": "List of member codes from policies"}}, "validation": {"rules": [{"id": "policies_exist", "type": "json_path", "path": "$.ListOfPolicyListSocial", "operator": "not_empty", "error_message": "No policies found for customer", "warning_only": true}]}, "retry": {"max_attempts": 2, "delay_seconds": 3}}}], "output": {"storage": {"enabled": true, "table": "CustomerPolicyList", "key_fields": ["customer_id"], "data_mapping": {"customer_id": "{{input.customer_id}}", "citizen_id": "{{citizen_id}}", "policies_data": "{{policies}}", "member_codes": "{{member_codes}}", "last_updated": "{{timestamp}}"}}, "response_format": {"type": "processed", "processor": "policy_list_processor", "include_metadata": true}}, "validation": {"input_schema": {"customer_id": {"type": "integer", "required": true, "min": 1, "description": "Customer ID from database"}, "platform_id": {"type": "integer", "required": true, "min": 1, "description": "Platform identity ID"}}, "business_rules": [{"id": "customer_exists", "type": "database_check", "query": "SELECT 1 FROM customer_customer WHERE customer_id = :customer_id", "error_message": "Customer not found in database"}, {"id": "platform_identity_exists", "type": "database_check", "query": "SELECT 1 FROM customer_customerplatformidentity WHERE id = :platform_id AND is_active = true", "error_message": "Platform identity not found or inactive"}]}, "metadata": {"created_by": "system", "created_at": "2024-01-01T00:00:00Z", "last_modified": "2024-01-01T00:00:00Z", "documentation_url": "https://docs.example.com/workflows/bvtpa-policy-list", "support_contact": "<EMAIL>"}}