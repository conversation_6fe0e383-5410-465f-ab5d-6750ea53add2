# Enhanced Workflow Configuration System

## Overview

The Enhanced Workflow Configuration System provides a comprehensive, configuration-driven architecture for managing business workflows. This system allows business users to create, modify, and manage workflows through JSON configuration files without requiring code changes.

## Key Features

### 🔧 **Configuration-Driven Architecture**
- All workflow logic externalized to JSON files
- Generic Python execution engine
- No code changes required for workflow modifications

### 📋 **V2 Schema Format**
- **V2.0**: Enhanced format with advanced features
- Comprehensive validation and business rules
- No legacy format support - V2 only

### ✅ **Comprehensive Validation**
- V2 JSON schema validation
- Business rule validation
- Step dependency checking
- Circular dependency detection

### 🔄 **Dynamic Workflow Management**
- Runtime workflow loading and reloading
- Workflow registry with caching
- Thread-safe operations

### 🛠 **Management Tools**
- Django management commands
- Configuration migration utilities
- Validation and testing tools

## Architecture Components

### 1. Workflow Registry (`workflow_registry.py`)
Central registry for managing workflow configurations:
- Dynamic workflow loading
- Validation and caching
- Thread-safe singleton pattern

### 2. Configuration Validator (`workflow_config_validator.py`)
Comprehensive validation system:
- V2 schema validation
- Business rule validation
- Step dependency validation

### 3. Configuration Manager (`workflow_config_manager.py`)
High-level management operations:
- Workflow validation
- Import/export functionality
- Statistics and reporting

### 4. Enhanced Workflow Service (`policy_workflow_service.py`)
V2-only execution engine:
- V2 format support with advanced features
- Step dependency resolution
- Configuration-driven execution

### 5. Management Commands (`manage_workflows.py`)
Django management commands:
- List workflows
- Validate configurations
- Create new workflows
- Show statistics

## Configuration Format

### V2 Format (Enhanced)
```json
{
  "schema_version": "2.0",
  "workflow": {
    "id": "workflow_id",
    "name": "Workflow Name",
    "version": "1.0.0",
    "description": "Workflow description",
    "category": "category_name"
  },
  "configuration": {
    "data_source": {
      "mode": "database",
      "database_queries": [...],
      "fixed_values": {...}
    },
    "execution": {
      "timeout_minutes": 5,
      "retry_policy": {...},
      "cache": {...}
    }
  },
  "steps": [
    {
      "id": "step_id",
      "name": "step_name",
      "type": "http_request",
      "depends_on": ["other_step"],
      "config": {
        "endpoint": "/api/endpoint",
        "method": "POST",
        "headers": {...},
        "request_body": {...},
        "response_extraction": {...},
        "validation": {...},
        "retry": {...}
      }
    }
  ],
  "output": {
    "storage": {...},
    "response_format": {...}
  },
  "validation": {
    "input_schema": {...},
    "business_rules": [...]
  }
}
```

## Usage Examples

### Loading Workflows

```python
from customer._services.workflow_registry import get_workflow_registry
from customer._services.policy_workflow_service import WorkflowConfigLoader

# Using registry (recommended)
registry = get_workflow_registry()
config = registry.get_workflow('bvtpa_policy_list')

# Using loader (backward compatible)
config = WorkflowConfigLoader.load_workflow_config('POLICY_LIST')
```

### Validating Configurations

```python
from customer._services.workflow_config_validator import WorkflowConfigValidator

result = WorkflowConfigValidator.validate_workflow_config(config)
if result.is_valid:
    print("Configuration is valid")
else:
    print(f"Validation errors: {result.errors}")
```

### Managing Workflows

```python
from customer._services.workflow_config_manager import WorkflowConfigManager

manager = WorkflowConfigManager()

# Get statistics
stats = manager.get_workflow_statistics()

# Migrate workflow to V2
success = manager.migrate_workflow_to_v2('workflow_id')

# Validate all workflows
results = manager.validate_all_workflows()
```

## Management Commands

### List Workflows
```bash
python manage.py manage_workflows list
python manage.py manage_workflows list --category policy_management
python manage.py manage_workflows list --include-invalid
```

### Validate Workflows
```bash
python manage.py manage_workflows validate
python manage.py manage_workflows validate --workflow-id bvtpa_policy_list
```

### Show Workflow Details
```bash
python manage.py manage_workflows show bvtpa_policy_list
```

### Migrate Workflows
```bash
python manage.py manage_workflows migrate workflow_id
python manage.py manage_workflows migrate workflow_id --no-backup
```

### View Statistics
```bash
python manage.py manage_workflows stats
```

### Reload Workflows
```bash
python manage.py manage_workflows reload
python manage.py manage_workflows reload --workflow-id bvtpa_policy_list
```

## V2 Configuration Features

### Enhanced Data Source Configuration
- **Database Mode**: Query database tables for dynamic data
- **Fixed Mode**: Use predefined static values
- **Hybrid Mode**: Combine database and fixed values

### Step Dependencies
- Define execution order through dependencies
- Automatic dependency resolution
- Circular dependency detection

### Advanced Validation
- JSON path-based validation rules
- Custom validation functions
- Warning vs. error handling

### Response Processing
- Flexible data extraction using JSON paths
- Type conversion and validation
- Required vs. optional fields

### Retry Configuration
- Per-step retry policies
- Configurable backoff strategies
- Timeout management

## V2 Workflow Creation Guide

### Creating New V2 Workflows

1. **Using Management Command**:
   ```bash
   python manage.py manage_workflows create my_workflow --template basic --name "My Workflow"
   ```

2. **Manual Creation**:
   ```python
   manager = WorkflowConfigManager()
   success = manager.create_workflow_from_template('basic', 'my_workflow', 'My Workflow')
   ```

### V2 Format Features
- Structured metadata section
- Enhanced configuration options
- Step dependencies and types
- Comprehensive validation
- Advanced validation rules
- Output configuration

## Testing

### Run Test Suite
```bash
cd customer/_services
python test_enhanced_workflows.py
```

### Test Components
- Workflow registry functionality
- Configuration validation
- Format detection
- Enhanced loading mechanisms

## Best Practices

### 1. Configuration Design
- Use descriptive workflow and step IDs
- Include comprehensive descriptions
- Organize workflows by category
- Version your configurations

### 2. Validation
- Always validate configurations before deployment
- Use business rules for domain-specific validation
- Handle warnings appropriately

### 3. Error Handling
- Configure appropriate retry policies
- Use warning-only validation for non-critical issues
- Provide meaningful error messages

### 4. Performance
- Enable caching for frequently used workflows
- Set appropriate timeouts
- Monitor workflow execution metrics

## Troubleshooting

### Common Issues

1. **Workflow Not Found**
   - Check workflow ID spelling
   - Ensure workflow file exists
   - Reload workflows if recently added

2. **Validation Errors**
   - Check JSON syntax
   - Verify required fields are present
   - Review business rule constraints

3. **Dependency Issues**
   - Check step dependencies are valid
   - Ensure no circular dependencies
   - Verify dependent steps exist

### Debug Mode
Enable debug logging to troubleshoot issues:
```python
import logging
logging.getLogger('django.customer_policy_crm').setLevel(logging.DEBUG)
```

## Future Enhancements

- Web-based workflow editor
- Visual workflow designer
- Advanced monitoring and metrics
- Workflow versioning and rollback
- Integration with external systems
- Custom step type plugins
