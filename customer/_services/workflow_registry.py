import json
import os
import logging
import threading
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime

from .workflow_config_validator import WorkflowConfigValidator, ValidationResult

logger = logging.getLogger('django.customer_policy_crm')


@dataclass
class WorkflowInfo:
    """Information about a registered workflow"""
    id: str
    name: str
    version: str
    description: str
    category: str
    schema_version: str
    file_path: str
    tags: List[str]
    loaded_at: datetime
    is_valid: bool
    validation_errors: List[str]


class WorkflowRegistry:
    """Registry for dynamically loading and managing workflow configurations"""
    
    def __init__(self, config_directory: str = None):
        """
        Initialize workflow registry
        
        Args:
            config_directory: Directory containing workflow JSON files.
                            If None, uses the same directory as this module.
        """
        if config_directory is None:
            config_directory = os.path.dirname(__file__)
        
        self.config_directory = config_directory
        self._workflows: Dict[str, Dict[str, Any]] = {}
        self._workflow_info: Dict[str, WorkflowInfo] = {}
        self._lock = threading.RLock()
        
        logger.info(f"WorkflowRegistry.__init__: Initializing registry with config directory: {config_directory}")
        self._load_workflows()
    
    def _load_workflows(self):
        """Load all workflow configurations from directory"""
        logger.debug("WorkflowRegistry._load_workflows: Starting workflow loading")
        
        with self._lock:
            self._workflows.clear()
            self._workflow_info.clear()
            
            if not os.path.exists(self.config_directory):
                logger.warning(f"WorkflowRegistry._load_workflows: Config directory does not exist: {self.config_directory}")
                return
            
            loaded_count = 0
            error_count = 0
            
            for config_file in os.listdir(self.config_directory):
                if not config_file.endswith('.json'):
                    continue
                
                try:
                    config_path = os.path.join(self.config_directory, config_file)
                    logger.debug(f"WorkflowRegistry._load_workflows: Loading config file: {config_file}")
                    
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # Validate configuration
                    validation_result = WorkflowConfigValidator.validate_workflow_config(config, config_path)
                    
                    if not validation_result.is_valid:
                        logger.error(f"WorkflowRegistry._load_workflows: Invalid workflow config {config_file}: {validation_result.errors}")
                        error_count += 1
                        
                        # Still register invalid workflows for debugging purposes
                        workflow_id = self._extract_workflow_id(config, config_file)
                        self._register_invalid_workflow(workflow_id, config, config_path, validation_result)
                        continue
                    
                    # V1 migration is no longer supported - only V2 format is accepted
                    
                    # Register valid workflow
                    workflow_id = config['workflow']['id']
                    self._workflows[workflow_id] = config
                    
                    # Create workflow info
                    workflow_metadata = config['workflow']
                    self._workflow_info[workflow_id] = WorkflowInfo(
                        id=workflow_id,
                        name=workflow_metadata['name'],
                        version=workflow_metadata['version'],
                        description=workflow_metadata.get('description', ''),
                        category=workflow_metadata.get('category', 'general'),
                        schema_version=validation_result.schema_version,
                        file_path=config_path,
                        tags=workflow_metadata.get('tags', []),
                        loaded_at=datetime.now(),
                        is_valid=True,
                        validation_errors=[]
                    )
                    
                    loaded_count += 1
                    logger.info(f"WorkflowRegistry._load_workflows: Successfully loaded workflow: {workflow_id}")
                    
                except Exception as e:
                    logger.error(f"WorkflowRegistry._load_workflows: Failed to load workflow config {config_file}: {str(e)}")
                    error_count += 1
            
            logger.info(f"WorkflowRegistry._load_workflows: Workflow loading completed - loaded={loaded_count}, errors={error_count}")
    
    def _extract_workflow_id(self, config: Dict[str, Any], filename: str) -> str:
        """Extract workflow ID from config or generate from filename"""
        if 'workflow' in config and 'id' in config['workflow']:
            return config['workflow']['id']
        else:
            # Fallback to filename (V1 format no longer supported)
            return os.path.splitext(filename)[0].replace('-', '_')
    
    def _register_invalid_workflow(self, workflow_id: str, config: Dict[str, Any], 
                                 config_path: str, validation_result: ValidationResult):
        """Register an invalid workflow for debugging purposes"""
        workflow_name = config.get('name', workflow_id)
        if 'workflow' in config:
            workflow_name = config['workflow'].get('name', workflow_name)
        
        self._workflow_info[workflow_id] = WorkflowInfo(
            id=workflow_id,
            name=workflow_name,
            version=config.get('version', '0.0.0'),
            description=f"Invalid workflow configuration",
            category='invalid',
            schema_version=validation_result.schema_version,
            file_path=config_path,
            tags=['invalid'],
            loaded_at=datetime.now(),
            is_valid=False,
            validation_errors=validation_result.errors
        )
    
    def get_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """
        Get workflow configuration by ID
        
        Args:
            workflow_id: Unique workflow identifier
            
        Returns:
            Workflow configuration dict or None if not found
        """
        with self._lock:
            config = self._workflows.get(workflow_id)
            if config:
                logger.debug(f"WorkflowRegistry.get_workflow: Retrieved workflow: {workflow_id}")
            else:
                logger.warning(f"WorkflowRegistry.get_workflow: Workflow not found: {workflow_id}")
            return config
    
    def get_workflow_info(self, workflow_id: str) -> Optional[WorkflowInfo]:
        """
        Get workflow information by ID
        
        Args:
            workflow_id: Unique workflow identifier
            
        Returns:
            WorkflowInfo object or None if not found
        """
        with self._lock:
            return self._workflow_info.get(workflow_id)
    
    def list_workflows(self, category: str = None, include_invalid: bool = False) -> List[WorkflowInfo]:
        """
        List all available workflows
        
        Args:
            category: Filter by category (optional)
            include_invalid: Whether to include invalid workflows
            
        Returns:
            List of WorkflowInfo objects
        """
        with self._lock:
            workflows = []
            
            for workflow_info in self._workflow_info.values():
                # Filter by validity
                if not include_invalid and not workflow_info.is_valid:
                    continue
                
                # Filter by category
                if category and workflow_info.category != category:
                    continue
                
                workflows.append(workflow_info)
            
            # Sort by name
            workflows.sort(key=lambda w: w.name)
            
            logger.debug(f"WorkflowRegistry.list_workflows: Listed {len(workflows)} workflows (category={category}, include_invalid={include_invalid})")
            return workflows
    
    def get_workflow_categories(self) -> List[str]:
        """Get list of all workflow categories"""
        with self._lock:
            categories = set()
            for workflow_info in self._workflow_info.values():
                if workflow_info.is_valid:  # Only include valid workflows
                    categories.add(workflow_info.category)
            
            return sorted(list(categories))
    
    def reload_workflows(self):
        """Reload all workflow configurations from disk"""
        logger.info("WorkflowRegistry.reload_workflows: Reloading all workflows")
        self._load_workflows()
    
    def reload_workflow(self, workflow_id: str) -> bool:
        """
        Reload a specific workflow configuration
        
        Args:
            workflow_id: Workflow to reload
            
        Returns:
            True if successfully reloaded, False otherwise
        """
        logger.info(f"WorkflowRegistry.reload_workflow: Reloading workflow: {workflow_id}")
        
        with self._lock:
            workflow_info = self._workflow_info.get(workflow_id)
            if not workflow_info:
                logger.warning(f"WorkflowRegistry.reload_workflow: Workflow not found: {workflow_id}")
                return False
            
            try:
                with open(workflow_info.file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # Validate configuration
                validation_result = WorkflowConfigValidator.validate_workflow_config(config, workflow_info.file_path)
                
                if not validation_result.is_valid:
                    logger.error(f"WorkflowRegistry.reload_workflow: Invalid workflow config for {workflow_id}: {validation_result.errors}")
                    # Update workflow info to reflect invalid state
                    workflow_info.is_valid = False
                    workflow_info.validation_errors = validation_result.errors
                    workflow_info.loaded_at = datetime.now()
                    return False
                
                # V1 migration is no longer supported - only V2 format is accepted
                
                # Update workflow
                self._workflows[workflow_id] = config
                
                # Update workflow info
                workflow_metadata = config['workflow']
                workflow_info.name = workflow_metadata['name']
                workflow_info.version = workflow_metadata['version']
                workflow_info.description = workflow_metadata.get('description', '')
                workflow_info.category = workflow_metadata.get('category', 'general')
                workflow_info.schema_version = validation_result.schema_version
                workflow_info.tags = workflow_metadata.get('tags', [])
                workflow_info.loaded_at = datetime.now()
                workflow_info.is_valid = True
                workflow_info.validation_errors = []
                
                logger.info(f"WorkflowRegistry.reload_workflow: Successfully reloaded workflow: {workflow_id}")
                return True
                
            except Exception as e:
                logger.error(f"WorkflowRegistry.reload_workflow: Failed to reload workflow {workflow_id}: {str(e)}")
                return False
    
    def validate_workflow(self, workflow_id: str) -> ValidationResult:
        """
        Validate a specific workflow configuration
        
        Args:
            workflow_id: Workflow to validate
            
        Returns:
            ValidationResult object
        """
        with self._lock:
            workflow_info = self._workflow_info.get(workflow_id)
            if not workflow_info:
                return ValidationResult(
                    is_valid=False,
                    errors=[f"Workflow not found: {workflow_id}"],
                    warnings=[],
                    schema_version="unknown"
                )
            
            config = self._workflows.get(workflow_id)
            if not config:
                return ValidationResult(
                    is_valid=False,
                    errors=workflow_info.validation_errors,
                    warnings=[],
                    schema_version=workflow_info.schema_version
                )
            
            return WorkflowConfigValidator.validate_workflow_config(config, workflow_info.file_path)


# Global registry instance
_registry_instance: Optional[WorkflowRegistry] = None
_registry_lock = threading.Lock()


def get_workflow_registry() -> WorkflowRegistry:
    """Get the global workflow registry instance (singleton)"""
    global _registry_instance
    
    if _registry_instance is None:
        with _registry_lock:
            if _registry_instance is None:
                _registry_instance = WorkflowRegistry()
    
    return _registry_instance


def reset_workflow_registry():
    """Reset the global workflow registry (mainly for testing)"""
    global _registry_instance
    
    with _registry_lock:
        _registry_instance = None
