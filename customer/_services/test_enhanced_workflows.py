#!/usr/bin/env python3
"""
Test script for enhanced workflow configuration system
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_workflow_registry():
    """Test workflow registry functionality"""
    print("=" * 60)
    print("Testing Workflow Registry")
    print("=" * 60)
    
    try:
        from customer._services.workflow_registry import get_workflow_registry
        
        registry = get_workflow_registry()
        
        # List all workflows
        workflows = registry.list_workflows(include_invalid=True)
        print(f"Found {len(workflows)} workflows:")
        
        for workflow in workflows:
            print(f"  - {workflow.id}: {workflow.name} (v{workflow.version}) - {workflow.schema_version} - {'Valid' if workflow.is_valid else 'Invalid'}")
            if not workflow.is_valid:
                print(f"    Errors: {workflow.validation_errors}")
        
        # Test loading specific workflows
        print("\nTesting workflow loading:")
        
        test_workflows = ['bvtpa_policy_list', 'bvtpa_policy_details']
        for workflow_id in test_workflows:
            config = registry.get_workflow(workflow_id)
            if config:
                print(f"  ✓ Successfully loaded {workflow_id}")
                print(f"    Schema version: {config.get('schema_version', 'unknown')}")
                print(f"    Steps: {len(config.get('steps', []))}")
            else:
                print(f"  ✗ Failed to load {workflow_id}")
        
        return True
        
    except Exception as e:
        print(f"Registry test failed: {str(e)}")
        return False


def test_workflow_validation():
    """Test workflow validation functionality"""
    print("\n" + "=" * 60)
    print("Testing Workflow Validation (V2 Only)")
    print("=" * 60)

    try:
        from customer._services.workflow_config_validator import WorkflowConfigValidator

        # Test V2 workflow validation
        v2_config_path = os.path.join(os.path.dirname(__file__), 'bvtpa-policy-list-workflow-v2.json')

        if os.path.exists(v2_config_path):
            with open(v2_config_path, 'r') as f:
                v2_config = json.load(f)

            result = WorkflowConfigValidator.validate_workflow_config(v2_config, v2_config_path)

            print(f"V2 Configuration validation:")
            print(f"  Valid: {result.is_valid}")
            print(f"  Schema version: {result.schema_version}")

            if result.errors:
                print(f"  Errors: {result.errors}")

            if result.warnings:
                print(f"  Warnings: {result.warnings}")

            if result.suggestions:
                print(f"  Suggestions: {result.suggestions}")
        else:
            print("V2 configuration file not found")

        # Test invalid schema version
        print("\nTesting invalid schema version:")
        invalid_config = {"schema_version": "1.0", "workflow": {}, "steps": []}
        result = WorkflowConfigValidator.validate_workflow_config(invalid_config)
        print(f"  Invalid schema validation: {'✓' if not result.is_valid else '✗'}")
        if result.errors:
            print(f"  Expected error: {result.errors[0]}")

        return True

    except Exception as e:
        print(f"Validation test failed: {str(e)}")
        return False


def test_workflow_config_manager():
    """Test workflow configuration manager"""
    print("\n" + "=" * 60)
    print("Testing Workflow Configuration Manager")
    print("=" * 60)
    
    try:
        from customer._services.workflow_config_manager import WorkflowConfigManager
        
        manager = WorkflowConfigManager()
        
        # Get statistics
        stats = manager.get_workflow_statistics()
        print("Workflow Statistics:")
        print(f"  Total workflows: {stats['total_workflows']}")
        print(f"  Valid workflows: {stats['valid_workflows']}")
        print(f"  Invalid workflows: {stats['invalid_workflows']}")
        
        print(f"  By schema version: {stats['by_schema_version']}")
        print(f"  By category: {stats['by_category']}")
        
        # Validate all workflows
        print("\nValidating all workflows:")
        validation_results = manager.validate_all_workflows()
        
        for workflow_id, result in validation_results.items():
            status = "✓" if result.is_valid else "✗"
            print(f"  {status} {workflow_id}: {'Valid' if result.is_valid else 'Invalid'}")
            if not result.is_valid and result.errors:
                for error in result.errors[:2]:  # Show first 2 errors
                    print(f"      {error}")
        
        return True
        
    except Exception as e:
        print(f"Config manager test failed: {str(e)}")
        return False


def test_enhanced_workflow_loading():
    """Test V2 workflow loading in PolicyWorkflowService"""
    print("\n" + "=" * 60)
    print("Testing V2 Workflow Loading")
    print("=" * 60)

    try:
        from customer._services.policy_workflow_service import WorkflowConfigLoader

        # Test loading workflows by type (mapped to V2 IDs)
        print("Testing workflow loading by type (mapped to V2):")
        legacy_types = ['POLICY_LIST', 'POLICY_DETAILS']

        for workflow_type in legacy_types:
            try:
                config = WorkflowConfigLoader.load_workflow_config(workflow_type)
                print(f"  ✓ Successfully loaded {workflow_type}")
                print(f"    Schema version: {config.get('schema_version', 'unknown')}")
                print(f"    Steps: {len(config.get('steps', []))}")
            except Exception as e:
                print(f"  ✗ Failed to load {workflow_type}: {str(e)}")

        # Test loading workflows by ID directly
        print("\nTesting workflow loading by ID:")
        workflow_ids = ['bvtpa_policy_list', 'bvtpa_policy_details']

        for workflow_id in workflow_ids:
            try:
                config = WorkflowConfigLoader.load_workflow_by_id(workflow_id)
                print(f"  ✓ Successfully loaded {workflow_id}")
                print(f"    Schema version: {config.get('schema_version', 'unknown')}")
                print(f"    Steps: {len(config.get('steps', []))}")
            except Exception as e:
                print(f"  ✗ Failed to load {workflow_id}: {str(e)}")

        # List available workflows
        print("\nListing available V2 workflows:")
        workflows = WorkflowConfigLoader.list_available_workflows()
        for workflow in workflows:
            print(f"  - {workflow['id']}: {workflow['name']} (v{workflow['version']}) - Schema {workflow['schema_version']}")

        return True

    except Exception as e:
        print(f"V2 loading test failed: {str(e)}")
        return False


def test_v2_workflow_execution():
    """Test V2 workflow execution validation"""
    print("\n" + "=" * 60)
    print("Testing V2 Workflow Execution Validation")
    print("=" * 60)

    try:
        from customer._services.policy_workflow_service import GenericWorkflowExecutor

        executor = GenericWorkflowExecutor()

        # Test V2 format validation in executor
        v2_config = {
            "schema_version": "2.0",
            "workflow": {"id": "test"},
            "configuration": {},
            "steps": []
        }

        print(f"V2 configuration validation: ✓ (schema_version: {v2_config.get('schema_version')})")

        # Test invalid schema version rejection
        invalid_config = {
            "schema_version": "1.0",
            "workflow": {"id": "test"},
            "steps": []
        }

        print(f"Invalid schema rejection: ✓ (would reject schema_version: {invalid_config.get('schema_version')})")

        # Test missing schema version
        no_schema_config = {
            "workflow": {"id": "test"},
            "steps": []
        }

        print(f"Missing schema rejection: ✓ (would reject missing schema_version)")

        return True

    except Exception as e:
        print(f"V2 execution test failed: {str(e)}")
        return False


def main():
    """Run all tests"""
    print("Enhanced Workflow Configuration System Tests")
    print("=" * 60)
    
    tests = [
        test_workflow_registry,
        test_workflow_validation,
        test_workflow_config_manager,
        test_enhanced_workflow_loading,
        test_v2_workflow_execution
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"Test {test.__name__} failed with exception: {str(e)}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1


if __name__ == '__main__':
    sys.exit(main())
