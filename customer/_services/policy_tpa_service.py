import requests
import time
import logging
import os
import json
from typing import Dict, Any, Optional

logger = logging.getLogger('django.customer_policy_crm')

class TPAApiService:
    """Service for TPA (Third Party Administrator) API integration"""

    # Configuration constants - moved to top for maintainability
    DEFAULT_TIMEOUT = 10 # seconds
    DEFAULT_BASE_URL = "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2"
    MAX_RETRIES = 1
    RETRY_DELAY = 3  # seconds
    DEFAULT_SSL_VERIFY = 'false'

    # API endpoints
    ENDPOINT_GET_TOKEN = "/api/GetToken"
    ENDPOINT_SEARCH_CITIZEN_ID = "/api/SearchCitizenID"
    ENDPOINT_CHECK_REGISTER = "/api/CheckRegister"
    ENDPOINT_POLICY_LIST_SOCIAL = "/api/PolicyListSocial"
    ENDPOINT_POLICY_DETAIL_SOCIAL = "/api/PolicyDetailSocial"

    # Content types
    CONTENT_TYPE_JSON = "application/json"
    CONTENT_TYPE_FORM_URLENCODED = "application/x-www-form-urlencoded"

    # HTTP methods
    HTTP_METHOD_POST = "POST"

    # Configuration file names
    CONFIG_FILES = [
        'bvtpa-policy-list-workflow.json',
        'bvtpa-policy-details-workflow.json'
    ]

    def __init__(self):
        logger.info("TPAApiService.__init__: Initializing TPA API service")

        self.session = requests.Session()
        # Set default timeout for all requests
        self.default_timeout = self.DEFAULT_TIMEOUT
        logger.debug(f"TPAApiService.__init__: Set default timeout to {self.default_timeout} seconds")

        # Load configuration from workflow files
        logger.debug("TPAApiService.__init__: Loading configuration from workflow files")
        self._load_configuration()

        # Configure SSL verification based on environment variables
        # For development/testing with expired certificates
        tpa_ssl_verify_env = self.DEFAULT_SSL_VERIFY
        self.verify_ssl = tpa_ssl_verify_env.lower() == 'true'

        # Apply SSL verification setting to the session
        self.session.verify = self.verify_ssl

        # Debug logging
        logger.info(f"TPAApiService.__init__: SSL verification enabled: {self.verify_ssl}")
        logger.debug(f"TPAApiService.__init__: Session verify setting: {self.session.verify}")

        if not self.verify_ssl:
            logger.warning("TPAApiService.__init__: SSL verification is disabled. This should only be used in development/testing.")
            # Disable SSL warnings when verification is disabled
            try:
                import urllib3
                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                logger.info("TPAApiService.__init__: SSL warnings disabled successfully")
            except ImportError:
                logger.warning("TPAApiService.__init__: urllib3 not available, SSL warnings cannot be disabled")

        logger.info("TPAApiService.__init__: TPA API service initialization completed successfully")

    def _load_configuration(self):
        """Load configuration from workflow JSON files"""
        logger.debug("TPAApiService._load_configuration: Starting configuration loading")

        try:
            # Get the directory where this file is located
            current_dir = os.path.dirname(os.path.abspath(__file__))
            logger.debug(f"TPAApiService._load_configuration: Looking for config files in directory: {current_dir}")

            # Try to load from policy list workflow first, then policy details workflow as fallback
            config_loaded = False
            for config_file in self.CONFIG_FILES:
                logger.debug(f"TPAApiService._load_configuration: Attempting to load config from {config_file}")
                try:
                    config_path = os.path.join(current_dir, config_file)
                    with open(config_path, 'r') as f:
                        workflow_config = json.load(f)

                    # Extract configuration from the first step (get_bearer_token)
                    token_step = workflow_config['steps'][0]
                    if token_step['name'] == 'get_bearer_token':
                        # Base URL is not in the workflow files, so we keep it hardcoded for now
                        # This could be moved to a separate config file in the future
                        self.base_url = self.DEFAULT_BASE_URL
                        self.username = token_step['request']['USERNAME']
                        self.password = token_step['request']['PASSWORD']

                        logger.info(f"TPAApiService._load_configuration: TPA configuration loaded successfully from {config_file}")
                        logger.debug(f"TPAApiService._load_configuration: Base URL: {self.base_url}")
                        logger.debug(f"TPAApiService._load_configuration: Username: {self.username}")
                        config_loaded = True
                        break

                except (FileNotFoundError, KeyError, IndexError) as e:
                    logger.debug(f"TPAApiService._load_configuration: Could not load configuration from {config_file}: {str(e)}")
                    continue

            if not config_loaded:
                logger.error("TPAApiService._load_configuration: No valid workflow configuration files found")
                raise Exception("No valid workflow configuration files found")

        except Exception as e:
            logger.error(f"TPAApiService._load_configuration: Failed to load TPA configuration from workflow files: {str(e)}")
            raise

    def load_workflow_config(self, workflow_file_path: str) -> Dict[str, Any]:
        """Load configuration from a specific workflow JSON file"""
        try:
            with open(workflow_file_path, 'r') as f:
                workflow_config = json.load(f)

            # Update credentials if the workflow contains authentication step
            for step in workflow_config.get('steps', []):
                if step.get('name') == 'get_bearer_token' and 'request' in step:
                    request_data = step['request']
                    if 'USERNAME' in request_data and 'PASSWORD' in request_data:
                        self.username = request_data['USERNAME']
                        self.password = request_data['PASSWORD']
                        logger.info(f"Updated TPA credentials from workflow file: {workflow_file_path}")
                        break

            return workflow_config

        except Exception as e:
            logger.error(f"Failed to load workflow configuration from {workflow_file_path}: {str(e)}")
            raise Exception(f"Workflow configuration loading failed: {str(e)}")

    def _make_request_with_retry(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make HTTP request with retry logic"""
        # Add default timeout if not specified
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.default_timeout

        # Debug logging
        logger.info(f"TPAApiService._make_request_with_retry: Starting request - Method: {method}, URL: {url}")
        logger.debug(f"TPAApiService._make_request_with_retry: Session verify: {self.session.verify}")
        logger.debug(f"TPAApiService._make_request_with_retry: Request timeout: {kwargs.get('timeout')}")

        # Log request headers (excluding sensitive data)
        headers = kwargs.get('headers', {})
        safe_headers = {k: ('***' if 'authorization' in k.lower() else v) for k, v in headers.items()}
        logger.debug(f"TPAApiService._make_request_with_retry: Request headers: {safe_headers}")

        last_exception = None
        for attempt in range(self.MAX_RETRIES):
            try:
                logger.debug(f"TPAApiService._make_request_with_retry: Attempt {attempt + 1}/{self.MAX_RETRIES}")
                start_time = time.time()

                response = self.session.request(method, url, **kwargs)
                response.raise_for_status()

                request_time = (time.time() - start_time) * 1000
                logger.info(f"TPAApiService._make_request_with_retry: Request successful on attempt {attempt + 1} - Status: {response.status_code}, Time: {request_time:.2f}ms")
                logger.debug(f"TPAApiService._make_request_with_retry: Response headers: {dict(response.headers)}")

                return response
            except requests.exceptions.RequestException as e:
                last_exception = e
                request_time = (time.time() - start_time) * 1000 if 'start_time' in locals() else 0
                logger.warning(f"TPAApiService._make_request_with_retry: Request failed (attempt {attempt + 1}/{self.MAX_RETRIES}) after {request_time:.2f}ms: {str(e)}")

                if attempt < self.MAX_RETRIES - 1:
                    backoff_delay = self.RETRY_DELAY * (2 ** attempt)
                    logger.debug(f"TPAApiService._make_request_with_retry: Waiting {backoff_delay}s before retry")
                    time.sleep(backoff_delay)  # Exponential backoff

        # If we get here, all retries failed
        logger.error(f"TPAApiService._make_request_with_retry: All {self.MAX_RETRIES} attempts failed for {method} {url}. Final error: {str(last_exception)}")
        raise last_exception or requests.exceptions.RequestException("All retry attempts failed")
    
    def get_bearer_token(self, social_id: str, channel_id: str, channel: str) -> str:
        """Get bearer token from TPA API"""
        url = f"{self.base_url}{self.ENDPOINT_GET_TOKEN}"
        logger.info(f"TPAApiService.get_bearer_token: Requesting bearer token for social_id={social_id}, channel_id={channel_id}, channel={channel}")

        payload = {
            "USERNAME": self.username,
            "PASSWORD": self.password,
            "SOCIAL_ID": social_id,
            "CHANNEL_ID": channel_id,
            "CHANNEL": channel
        }

        # Log payload without sensitive data
        safe_payload = {k: ('***' if k in ['PASSWORD'] else v) for k, v in payload.items()}
        logger.debug(f"TPAApiService.get_bearer_token: Request payload: {safe_payload}")

        headers = {
            "Content-Type": self.CONTENT_TYPE_FORM_URLENCODED
        }

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, data=payload, headers=headers)
            token_data = response.text.strip()
            logger.info(f"TPAApiService.get_bearer_token: Successfully obtained bearer token for social_id={social_id}")
            logger.debug(f"TPAApiService.get_bearer_token: Token length: {len(token_data)} characters")
            return token_data
        except Exception as e:
            logger.error(f"TPAApiService.get_bearer_token: Failed to get TPA bearer token for social_id={social_id}: {str(e)}")
            raise Exception(f"TPA authentication failed: {str(e)}")
    
    def verify_citizen_id(self, token: str, citizen_id: str) -> Dict[str, Any]:
        """Verify citizen ID with TPA API"""
        url = f"{self.base_url}{self.ENDPOINT_SEARCH_CITIZEN_ID}"
        logger.info(f"TPAApiService.verify_citizen_id: Verifying citizen ID: {citizen_id}")

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        payload = {
            "CitizenID": citizen_id
        }

        logger.debug(f"TPAApiService.verify_citizen_id: Request payload: {payload}")

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, json=payload, headers=headers)
            response_data = response.json()

            # Log response summary without sensitive data
            if isinstance(response_data, dict):
                search_results = response_data.get('ListOfSearchCitizenID', [])
                logger.info(f"TPAApiService.verify_citizen_id: Citizen ID verification completed for {citizen_id} - Found {len(search_results)} results")
                if search_results:
                    first_result = search_results[0]
                    status = first_result.get('Status', 'unknown')
                    logger.debug(f"TPAApiService.verify_citizen_id: First result status: {status}")

            return response_data
        except Exception as e:
            logger.error(f"TPAApiService.verify_citizen_id: Failed to verify citizen ID {citizen_id}: {str(e)}")
            raise Exception(f"Citizen ID verification failed: {str(e)}")
    
    def check_registration(self, token: str, citizen_id: str, social_id: str, channel_id: str) -> Dict[str, Any]:
        """Check registration status with TPA API"""
        url = f"{self.base_url}{self.ENDPOINT_CHECK_REGISTER}"
        logger.info(f"TPAApiService.check_registration: Checking registration for citizen_id={citizen_id}, social_id={social_id}, channel_id={channel_id}")

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id
        }

        logger.debug(f"TPAApiService.check_registration: Request payload: {payload}")

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, json=payload, headers=headers)
            response_data = response.json()

            # Log response summary
            if isinstance(response_data, dict):
                check_results = response_data.get('ListOfCheckRegister', [])
                logger.info(f"TPAApiService.check_registration: Registration check completed for citizen_id={citizen_id} - Found {len(check_results)} results")
                if check_results:
                    first_result = check_results[0]
                    status = first_result.get('Status', 'unknown')
                    logger.debug(f"TPAApiService.check_registration: Registration status: {status}")

            return response_data
        except Exception as e:
            logger.error(f"TPAApiService.check_registration: Failed to check registration for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Registration check failed: {str(e)}")
    
    def get_policy_list(self, token: str, citizen_id: str, social_id: str, channel_id: str) -> Dict[str, Any]:
        """Get policy list from TPA API"""
        url = f"{self.base_url}{self.ENDPOINT_POLICY_LIST_SOCIAL}"
        logger.info(f"TPAApiService.get_policy_list: Retrieving policy list for citizen_id={citizen_id}, social_id={social_id}, channel_id={channel_id}")

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id
        }

        logger.debug(f"TPAApiService.get_policy_list: Request payload: {payload}")

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, json=payload, headers=headers)
            response_data = response.json()

            # Log response summary
            if isinstance(response_data, dict):
                policy_list = response_data.get('ListOfPolicyListSocial', [])
                logger.info(f"TPAApiService.get_policy_list: Policy list retrieval completed for citizen_id={citizen_id} - Found {len(policy_list)} policies")

                # Log member codes found
                member_codes = []
                for policy in policy_list:
                    if isinstance(policy, dict) and 'MemberCode' in policy:
                        member_code = policy['MemberCode']
                        if member_code not in member_codes:
                            member_codes.append(member_code)

                logger.debug(f"TPAApiService.get_policy_list: Found {len(member_codes)} unique member codes: {member_codes}")

            return response_data
        except Exception as e:
            logger.error(f"TPAApiService.get_policy_list: Failed to get policy list for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Policy list retrieval failed: {str(e)}")
    
    def get_policy_details(self, token: str, citizen_id: str, social_id: str, channel_id: str, member_code: str) -> Dict[str, Any]:
        """Get policy details from TPA API"""
        url = f"{self.base_url}{self.ENDPOINT_POLICY_DETAIL_SOCIAL}"
        logger.info(f"TPAApiService.get_policy_details: Retrieving policy details for citizen_id={citizen_id}, social_id={social_id}, channel_id={channel_id}, member_code={member_code}")

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id,
            "MemberCode": member_code
        }

        logger.debug(f"TPAApiService.get_policy_details: Request payload: {payload}")

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, json=payload, headers=headers)
            response_data = response.json()

            # Log response summary
            if isinstance(response_data, dict):
                policy_details = response_data.get('ListOfPolDet', [])
                policy_claims = response_data.get('ListOfPolClaim', [])
                logger.info(f"TPAApiService.get_policy_details: Policy details retrieval completed for member_code={member_code} - Found {len(policy_details)} policy details and {len(policy_claims)} claims")

            return response_data
        except Exception as e:
            logger.error(f"TPAApiService.get_policy_details: Failed to get policy details for member {member_code}: {str(e)}")
            raise Exception(f"Policy details retrieval failed: {str(e)}")

    # Dynamic request methods for JSON-driven workflows
    def make_dynamic_request(self, endpoint: str, method: str, payload: Dict[str, Any],
                           headers: Optional[Dict[str, str]] = None) -> Any:
        """Make a dynamic API request with configurable payload and headers"""
        url = f"{self.base_url}{endpoint}"
        logger.info(f"TPAApiService.make_dynamic_request: Making dynamic request - endpoint={endpoint}, method={method}")

        # Default headers
        request_headers = {
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        # Add custom headers if provided
        if headers:
            request_headers.update(headers)

        # Log request details (excluding sensitive data)
        safe_payload = {k: ('***' if k in ['PASSWORD'] else v) for k, v in payload.items()}
        safe_headers = {k: ('***' if 'authorization' in k.lower() else v) for k, v in request_headers.items()}
        logger.debug(f"TPAApiService.make_dynamic_request: Request payload: {safe_payload}")
        logger.debug(f"TPAApiService.make_dynamic_request: Request headers: {safe_headers}")

        try:
            # Determine request method and payload format
            if method.upper() == self.HTTP_METHOD_POST:
                if request_headers.get("Content-Type") == self.CONTENT_TYPE_FORM_URLENCODED:
                    logger.debug(f"TPAApiService.make_dynamic_request: Using form-urlencoded payload")
                    response = self._make_request_with_retry(method, url, data=payload, headers=request_headers)
                else:
                    logger.debug(f"TPAApiService.make_dynamic_request: Using JSON payload")
                    response = self._make_request_with_retry(method, url, json=payload, headers=request_headers)
            else:
                logger.debug(f"TPAApiService.make_dynamic_request: Using query parameters")
                response = self._make_request_with_retry(method, url, params=payload, headers=request_headers)

            # Handle different response types
            content_type = response.headers.get('content-type', '').lower()
            if self.CONTENT_TYPE_JSON in content_type:
                logger.debug(f"TPAApiService.make_dynamic_request: Parsing JSON response")
                response_data = response.json()
                logger.info(f"TPAApiService.make_dynamic_request: Dynamic request completed successfully - endpoint={endpoint}, response_type=json")
                return response_data
            else:
                # For token endpoint which returns plain text
                logger.debug(f"TPAApiService.make_dynamic_request: Parsing text response")
                response_text = response.text.strip().replace('"', '')
                logger.info(f"TPAApiService.make_dynamic_request: Dynamic request completed successfully - endpoint={endpoint}, response_type=text, length={len(response_text)}")
                return response_text

        except Exception as e:
            logger.error(f"TPAApiService.make_dynamic_request: Dynamic API request failed for {endpoint}: {str(e)}")
            raise Exception(f"API request to {endpoint} failed: {str(e)}")


