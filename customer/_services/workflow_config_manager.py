import json
import os
import shutil
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .workflow_registry import get_workflow_registry, WorkflowRegistry
from .workflow_config_validator import WorkflowConfigValidator, ValidationResult
from customer.exceptions import PolicyWorkflowError

logger = logging.getLogger('django.customer_policy_crm')


class WorkflowConfigManager:
    """Manager for workflow configuration operations"""
    
    def __init__(self, config_directory: str = None):
        """
        Initialize workflow configuration manager
        
        Args:
            config_directory: Directory containing workflow JSON files
        """
        if config_directory is None:
            config_directory = os.path.dirname(__file__)
        
        self.config_directory = config_directory
        self.registry = get_workflow_registry()
        
        logger.info(f"WorkflowConfigManager.__init__: Initialized with config directory: {config_directory}")
    
    def validate_all_workflows(self) -> Dict[str, ValidationResult]:
        """
        Validate all workflow configurations
        
        Returns:
            Dictionary mapping workflow IDs to validation results
        """
        logger.info("WorkflowConfigManager.validate_all_workflows: Starting validation of all workflows")
        
        results = {}
        workflows = self.registry.list_workflows(include_invalid=True)
        
        for workflow in workflows:
            try:
                validation_result = self.registry.validate_workflow(workflow.id)
                results[workflow.id] = validation_result
                
                if validation_result.is_valid:
                    logger.debug(f"WorkflowConfigManager.validate_all_workflows: Workflow {workflow.id} is valid")
                else:
                    logger.warning(f"WorkflowConfigManager.validate_all_workflows: Workflow {workflow.id} has validation errors: {validation_result.errors}")
                    
            except Exception as e:
                logger.error(f"WorkflowConfigManager.validate_all_workflows: Failed to validate workflow {workflow.id}: {str(e)}")
                results[workflow.id] = ValidationResult(
                    is_valid=False,
                    errors=[f"Validation failed: {str(e)}"],
                    warnings=[],
                    schema_version="unknown"
                )
        
        valid_count = sum(1 for result in results.values() if result.is_valid)
        total_count = len(results)
        
        logger.info(f"WorkflowConfigManager.validate_all_workflows: Validation completed - {valid_count}/{total_count} workflows are valid")
        return results
    
    def validate_workflow(self, workflow_id: str) -> ValidationResult:
        """
        Validate a specific workflow configuration

        Args:
            workflow_id: ID of workflow to validate

        Returns:
            ValidationResult with validation status and details
        """
        logger.info(f"WorkflowConfigManager.validate_workflow: Validating workflow: {workflow_id}")

        try:
            workflow_info = self.registry.get_workflow_info(workflow_id)
            if not workflow_info:
                logger.error(f"WorkflowConfigManager.validate_workflow: Workflow not found: {workflow_id}")
                return ValidationResult(
                    is_valid=False,
                    errors=[f"Workflow not found: {workflow_id}"],
                    warnings=[],
                    suggestions=[],
                    schema_version="unknown"
                )

            # Load and validate configuration
            with open(workflow_info.file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            validation_result = WorkflowConfigValidator.validate_workflow_config(config, workflow_info.file_path)

            logger.info(f"WorkflowConfigManager.validate_workflow: Validation completed for {workflow_id} - valid={validation_result.is_valid}")
            return validation_result

        except Exception as e:
            logger.error(f"WorkflowConfigManager.validate_workflow: Validation failed for workflow {workflow_id}: {str(e)}")
            return ValidationResult(
                is_valid=False,
                errors=[f"Validation failed: {str(e)}"],
                warnings=[],
                suggestions=[],
                schema_version="unknown"
            )
    
    def create_workflow_from_template(self, template_name: str, workflow_id: str, 
                                    workflow_name: str, **kwargs) -> bool:
        """
        Create a new workflow from a template
        
        Args:
            template_name: Name of template to use
            workflow_id: ID for new workflow
            workflow_name: Display name for new workflow
            **kwargs: Additional parameters for template customization
            
        Returns:
            True if creation successful, False otherwise
        """
        logger.info(f"WorkflowConfigManager.create_workflow_from_template: Creating workflow {workflow_id} from template {template_name}")
        
        try:
            # Load template
            template_path = os.path.join(self.config_directory, 'templates', f"{template_name}.json")
            if not os.path.exists(template_path):
                logger.error(f"WorkflowConfigManager.create_workflow_from_template: Template not found: {template_path}")
                return False
            
            with open(template_path, 'r', encoding='utf-8') as f:
                template_config = json.load(f)
            
            # Customize template
            template_config['workflow']['id'] = workflow_id
            template_config['workflow']['name'] = workflow_name
            template_config['workflow']['version'] = '1.0.0'
            template_config['metadata']['created_at'] = datetime.now().isoformat()
            template_config['metadata']['last_modified'] = datetime.now().isoformat()
            
            # Apply additional customizations
            for key, value in kwargs.items():
                if key in template_config:
                    template_config[key] = value
            
            # Validate configuration
            validation_result = WorkflowConfigValidator.validate_workflow_config(template_config)
            if not validation_result.is_valid:
                logger.error(f"WorkflowConfigManager.create_workflow_from_template: Template configuration is invalid: {validation_result.errors}")
                return False
            
            # Write new workflow file
            workflow_file_path = os.path.join(self.config_directory, f"{workflow_id}.json")
            with open(workflow_file_path, 'w', encoding='utf-8') as f:
                json.dump(template_config, f, indent=2, ensure_ascii=False)
            
            # Reload registry to include new workflow
            self.registry.reload_workflows()
            
            logger.info(f"WorkflowConfigManager.create_workflow_from_template: Successfully created workflow: {workflow_id}")
            return True
            
        except Exception as e:
            logger.error(f"WorkflowConfigManager.create_workflow_from_template: Failed to create workflow {workflow_id}: {str(e)}")
            return False
    
    def export_workflow(self, workflow_id: str, export_path: str, include_metadata: bool = True) -> bool:
        """
        Export a workflow configuration to a file
        
        Args:
            workflow_id: ID of workflow to export
            export_path: Path where to save exported configuration
            include_metadata: Whether to include metadata in export
            
        Returns:
            True if export successful, False otherwise
        """
        logger.info(f"WorkflowConfigManager.export_workflow: Exporting workflow {workflow_id} to {export_path}")
        
        try:
            config = self.registry.get_workflow(workflow_id)
            if not config:
                logger.error(f"WorkflowConfigManager.export_workflow: Workflow not found: {workflow_id}")
                return False
            
            # Create export configuration
            export_config = config.copy()
            
            if not include_metadata:
                export_config.pop('metadata', None)
            
            # Add export metadata
            export_config['export_info'] = {
                'exported_at': datetime.now().isoformat(),
                'exported_by': 'WorkflowConfigManager',
                'original_workflow_id': workflow_id
            }
            
            # Write export file
            os.makedirs(os.path.dirname(export_path), exist_ok=True)
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"WorkflowConfigManager.export_workflow: Successfully exported workflow {workflow_id}")
            return True
            
        except Exception as e:
            logger.error(f"WorkflowConfigManager.export_workflow: Failed to export workflow {workflow_id}: {str(e)}")
            return False
    
    def import_workflow(self, import_path: str, workflow_id: str = None, 
                       overwrite: bool = False) -> Optional[str]:
        """
        Import a workflow configuration from a file
        
        Args:
            import_path: Path to workflow configuration file
            workflow_id: ID to assign to imported workflow (optional)
            overwrite: Whether to overwrite existing workflow
            
        Returns:
            Workflow ID if import successful, None otherwise
        """
        logger.info(f"WorkflowConfigManager.import_workflow: Importing workflow from {import_path}")
        
        try:
            if not os.path.exists(import_path):
                logger.error(f"WorkflowConfigManager.import_workflow: Import file not found: {import_path}")
                return None
            
            # Load import configuration
            with open(import_path, 'r', encoding='utf-8') as f:
                import_config = json.load(f)
            
            # Determine workflow ID
            if workflow_id is None:
                workflow_id = import_config.get('workflow', {}).get('id')
                if not workflow_id:
                    logger.error("WorkflowConfigManager.import_workflow: No workflow ID specified and none found in import file")
                    return None
            else:
                # Update configuration with new ID
                if 'workflow' in import_config:
                    import_config['workflow']['id'] = workflow_id
            
            # Check if workflow already exists
            existing_workflow = self.registry.get_workflow(workflow_id)
            if existing_workflow and not overwrite:
                logger.error(f"WorkflowConfigManager.import_workflow: Workflow {workflow_id} already exists and overwrite=False")
                return None
            
            # Validate configuration
            validation_result = WorkflowConfigValidator.validate_workflow_config(import_config)
            if not validation_result.is_valid:
                logger.error(f"WorkflowConfigManager.import_workflow: Import configuration is invalid: {validation_result.errors}")
                return None
            
            # Clean up import metadata
            import_config.pop('export_info', None)
            
            # Update metadata
            if 'metadata' in import_config:
                import_config['metadata']['last_modified'] = datetime.now().isoformat()
            
            # Write workflow file
            workflow_file_path = os.path.join(self.config_directory, f"{workflow_id}.json")
            with open(workflow_file_path, 'w', encoding='utf-8') as f:
                json.dump(import_config, f, indent=2, ensure_ascii=False)
            
            # Reload registry
            self.registry.reload_workflows()
            
            logger.info(f"WorkflowConfigManager.import_workflow: Successfully imported workflow: {workflow_id}")
            return workflow_id
            
        except Exception as e:
            logger.error(f"WorkflowConfigManager.import_workflow: Failed to import workflow: {str(e)}")
            return None
    
    def get_workflow_statistics(self) -> Dict[str, Any]:
        """Get statistics about workflow configurations"""
        workflows = self.registry.list_workflows(include_invalid=True)
        
        stats = {
            'total_workflows': len(workflows),
            'valid_workflows': len([w for w in workflows if w.is_valid]),
            'invalid_workflows': len([w for w in workflows if not w.is_valid]),
            'by_schema_version': {},
            'by_category': {},
            'by_status': {
                'valid': 0,
                'invalid': 0
            }
        }
        
        for workflow in workflows:
            # Count by schema version
            version = workflow.schema_version
            stats['by_schema_version'][version] = stats['by_schema_version'].get(version, 0) + 1
            
            # Count by category
            category = workflow.category
            stats['by_category'][category] = stats['by_category'].get(category, 0) + 1
            
            # Count by status
            if workflow.is_valid:
                stats['by_status']['valid'] += 1
            else:
                stats['by_status']['invalid'] += 1
        
        return stats
